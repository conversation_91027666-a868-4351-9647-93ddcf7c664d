<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>Our Clients - Precium</title>
	<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,user-scalable=no">
	<meta name="description"
		content="Discover our extensive client portfolio. We serve 500+ hotels with 50,000+ inventory across 4+ continents, providing AI-powered revenue management solutions." />
	<meta name="keywords" content="hotel clients, revenue management, hotel portfolio, precium clients" />
	<link rel="canonical" href="https://precium.in/our-clients" />
	<link rel="apple-touch-icon" href="/images/logo-purple.svg" />
	<link rel="icon" href="/images/logo-purple.ico" />
	<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
		integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.3/dist/umd/popper.min.js"
		integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.1.3/dist/js/bootstrap.min.js"
		integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
		crossorigin="anonymous"></script>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css"
		integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">

	<link rel="stylesheet" type="text/css" href="/css/style.css">
	<link rel="stylesheet" type="text/css" href="/our-clients/style.css">

	<script src="/js/header.js"></script>
	<script src="/js/footer.js"></script>
</head>

<body>
	<my-header></my-header>

	<!-- Hero Section -->
	<div class="clients-hero">
		<div class="clients-hero-content">
			<div class="clients-hero-text">
				<h1>Our Clients</h1>
				<p>An organisation knowledge is one of the valuable asset. At Precium, We provide business insights to
					revenue managers to make right decision to support revenue growth</p>
			</div>
			<div class="clients-hero-image">
				<img src="/images/Vector 1.svg" alt="Our Clients">
			</div>
		</div>
	</div>

	<!-- Main Content Section -->
	<section class="clients-main">
		<div class="clients-stats">
			<h2>Serving 500+ Hotels with 50000 + inventory in 4+ Continents</h2>
		</div>

		<!-- Client Logos Grid -->
		<div class="clients-grid-container">
			<button class="nav-arrow nav-arrow-left" id="prevBtn">
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round" />
				</svg>
			</button>

			<div class="clients-grid-wrapper">
				<div class="clients-grid" id="clientsGrid">
					<!-- All 27 Client Icons -->
					<div class="client-logo">
						<img src="/images/client-Icons/client-image1.svg" alt="Client 1" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image2.svg" alt="Client 2" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image3.svg" alt="Client 3" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image4.svg" alt="Client 4" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image5.svg" alt="Client 5" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image6.svg" alt="Client 6" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image7.svg" alt="Client 7" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image8.svg" alt="Client 8" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image9.svg" alt="Client 9" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image10.svg" alt="Client 10" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image11.svg" alt="Client 11" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image12.svg" alt="Client 12" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image13.svg" alt="Client 13" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image14.svg" alt="Client 14" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image15.svg" alt="Client 15" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image16.svg" alt="Client 16" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image17.svg" alt="Client 17" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image18.svg" alt="Client 18" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image19.svg" alt="Client 19" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image20.svg" alt="Client 20" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image21.svg" alt="Client 21" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image22.svg" alt="Client 22" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image23.svg" alt="Client 23" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image24.svg" alt="Client 24" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image25.svg" alt="Client 25" />
					</div>

					<div class="client-logo">
						<img src="/images/client-Icons/client-image27.svg" alt="Client 27" />
					</div>
					<div class="client-logo">
						<img src="/images/client-Icons/client-image28.svg" alt="Client 28" />
					</div>
				</div>
			</div>

			<button class="nav-arrow nav-arrow-right" id="nextBtn">
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round" />
				</svg>
			</button>
		</div>
	</section>

	<my-footer></my-footer>

	<script src="/js/stickUp.min.js"></script>
	<script type="text/javascript">
		jQuery(function (a) { a(document).ready(function () { a(".navbar-inverse").stickUp() }) });
	</script>

	<script>
		// Client logos carousel functionality
		document.addEventListener('DOMContentLoaded', function () {
			const grid = document.getElementById('clientsGrid');
			const prevBtn = document.getElementById('prevBtn');
			const nextBtn = document.getElementById('nextBtn');
			let currentIndex = 0;
			const totalItems = grid.children.length;
			console.log({ totalItems });
			// Responsive items per view
			function getItemsPerView() {
				if (window.innerWidth <= 480) return 4;
				if (window.innerWidth <= 768) return 5;
				if (window.innerWidth <= 900) return 6;
				return 8; // 8 logos visible at once as in original design
			}

			// Calculate initial maxIndex based on actual grid layout
			const gridWrapper = document.querySelector('.clients-grid-wrapper');
			const wrapperWidth = gridWrapper.offsetWidth;
			const columnWidth = 5 * window.innerWidth / 100; // 5vw
			const gapWidth = 48; // 3rem gap
			const totalColumnWidth = columnWidth + gapWidth;
			const visibleColumns = Math.floor(wrapperWidth / totalColumnWidth);
			const totalColumns = Math.ceil(totalItems / 3); // Items arranged in 3 rows
			let maxIndex = Math.max(0, totalColumns - visibleColumns);

			function updateGrid() {
				// Calculate actual column width including gap
				const columnWidth = 5 * window.innerWidth / 100; // 5vw
				const gapWidth = 48; // 3rem gap (assuming 16px base font)
				const totalColumnWidth = columnWidth + gapWidth;

				// Ensure currentIndex never goes below 0 to prevent left overflow
				currentIndex = Math.max(0, currentIndex);

				// Calculate translateX - ensure it never goes positive
				const translateX = Math.min(0, -(currentIndex * totalColumnWidth));
				grid.style.transform = `translateX(${translateX}px)`;

				console.log({ currentIndex, maxIndex, translateX, totalColumnWidth, visibleColumns, totalColumns });

				// Update button states
				prevBtn.disabled = currentIndex === 0;
				nextBtn.disabled = currentIndex >= maxIndex+1;
			}

			prevBtn.addEventListener('click', function () {
				if (currentIndex > 0) {
					currentIndex -= 1; // Move one column at a time
					updateGrid();
				}
			});

			nextBtn.addEventListener('click', function () {
				if (currentIndex < maxIndex+1) {
					currentIndex += 1; // Move one column at a time
					updateGrid();
				}
			});

			// Handle window resize
			window.addEventListener('resize', function () {
				const gridWrapper = document.querySelector('.clients-grid-wrapper');
				const wrapperWidth = gridWrapper.offsetWidth;
				const columnWidth = 5 * window.innerWidth / 100; // 5vw
				const gapWidth = 48; // 3rem gap
				const totalColumnWidth = columnWidth + gapWidth;

				// Calculate how many full columns can fit in the wrapper
				const visibleColumns = Math.floor(wrapperWidth / totalColumnWidth);
				const totalColumns = Math.ceil(totalItems / 3); // Items arranged in 3 rows
				maxIndex = Math.max(0, totalColumns - visibleColumns);

				// Ensure currentIndex stays within bounds
				currentIndex = Math.max(0, Math.min(currentIndex, maxIndex));
				updateGrid();
			});

			// Initialize
			updateGrid();
		});
	</script>
</body>

</html>